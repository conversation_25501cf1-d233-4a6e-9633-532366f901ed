---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]

# Metadatos ICFES
icfes:
  competencia:
    - interpretacion_representacion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: laboral
  eje_axial: eje4
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}",
  "\\usepackage{amsmath}",
  "\\usepackage{array}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos
generar_datos <- function() {
  # Contextos aleatorios para exportaciones industriales
  contextos <- list(
    list(sector = "textil", productos = c("algodón", "lana", "seda", "fibras sintéticas"), unidad = "millones de dólares"),
    list(sector = "alimentario", productos = c("café", "cacao", "frutas", "cereales"), unidad = "millones de dólares"),
    list(sector = "minero", productos = c("carbón", "petróleo", "oro", "cobre"), unidad = "millones de dólares"),
    list(sector = "manufacturero", productos = c("maquinaria", "equipos", "vehículos", "electrodomésticos"), unidad = "millones de dólares"),
    list(sector = "químico", productos = c("fertilizantes", "plásticos", "medicamentos", "cosméticos"), unidad = "millones de dólares")
  )
  
  contexto_sel <- sample(contextos, 1)[[1]]
  
  # Países/regiones de destino aleatorios
  paises_opciones <- list(
    c("Estados Unidos", "Canadá", "México", "Brasil"),
    c("España", "Francia", "Alemania", "Italia"),
    c("China", "Japón", "Corea del Sur", "India"),
    c("Reino Unido", "Países Bajos", "Bélgica", "Suiza")
  )
  
  paises_sel <- sample(paises_opciones, 1)[[1]]
  resto_mundo <- "Resto del mundo"
  
  # Generar datos de exportación para 3 años
  años <- c(2008, 2009, 2010)
  
  # Generar valores base realistas
  base_2008 <- sample(600:900, 4)
  base_2009 <- base_2008 + sample(-100:100, 4)
  base_2010 <- base_2009 + sample(-50:150, 4)
  
  # Asegurar valores positivos
  base_2009 <- pmax(base_2009, 200)
  base_2010 <- pmax(base_2010, 200)
  
  # Crear estructura de datos
  datos_exportacion <- data.frame(
    pais = c(resto_mundo, paises_sel),
    año_2008 = c(sample(700:900, 1), base_2008),
    año_2009 = c(sample(650:850, 1), base_2009),
    año_2010 = c(sample(750:950, 1), base_2010),
    stringsAsFactors = FALSE
  )
  
  # Colores para cada país/región
  colores_disponibles <- c(
    "255,165,0",    # Naranja
    "0,128,255",    # Azul
    "255,0,128",    # Rosa
    "128,255,0",    # Verde lima
    "128,0,255"     # Púrpura
  )
  
  colores_asignados <- sample(colores_disponibles, 5)
  
  return(list(
    contexto = contexto_sel,
    paises = c(resto_mundo, paises_sel),
    datos = datos_exportacion,
    colores = colores_asignados,
    años = años
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
contexto_sector <- datos$contexto$sector
productos_ejemplo <- paste(datos$contexto$productos[1:2], collapse = " y ")
unidad_medida <- datos$contexto$unidad
paises_destino <- datos$paises
datos_exportacion <- datos$datos
colores_paises <- datos$colores
años_analisis <- datos$años
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
```

```{r generar_grafico_principal, echo=FALSE, results="hide"}
# Generar gráfico principal de barras horizontales usando Python
codigo_python_principal <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

# Datos de exportación
paises = ['", paste(datos_exportacion$pais, collapse = "', '"), "']
valores_2008 = [", paste(datos_exportacion$año_2008, collapse = ", "), "]
valores_2009 = [", paste(datos_exportacion$año_2009, collapse = ", "), "]
valores_2010 = [", paste(datos_exportacion$año_2010, collapse = ", "), "]

# Colores en formato matplotlib
colores = [", paste(sprintf("'#%02X%02X%02X'",
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[1])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[2])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[3]))
), collapse = ", "), "]

# Crear figura principal
fig, ax = plt.subplots(figsize=(12, 8))

# Configurar posiciones de las barras
y_pos = np.arange(len(paises))
bar_height = 0.25

# Crear barras horizontales agrupadas
bars_2008 = ax.barh(y_pos - bar_height, valores_2008, bar_height, 
                   label='2008', color=colores, alpha=0.8)
bars_2009 = ax.barh(y_pos, valores_2009, bar_height, 
                   label='2009', color=colores, alpha=0.6)
bars_2010 = ax.barh(y_pos + bar_height, valores_2010, bar_height, 
                   label='2010', color=colores, alpha=0.4)

# Configurar etiquetas
ax.set_yticks(y_pos)
ax.set_yticklabels(paises, fontsize=10)
ax.set_xlabel('Exportaciones (", unidad_medida, ")', fontsize=12, fontweight='bold')
ax.set_title('Exportaciones del sector ", contexto_sector, " por destino', fontsize=14, fontweight='bold')

# Añadir valores en las barras
for bars in [bars_2008, bars_2009, bars_2010]:
    for bar in bars:
        width = bar.get_width()
        ax.text(width + 10, bar.get_y() + bar.get_height()/2,
                f'{int(width)}', ha='left', va='center', fontsize=9)

# Configurar leyenda
ax.legend(loc='lower right')

# Configurar límites y grilla
max_valor = max(max(valores_2008), max(valores_2009), max(valores_2010))
ax.set_xlim(0, max_valor + 100)
ax.grid(axis='x', alpha=0.3)

plt.tight_layout()
plt.savefig('grafico_exportaciones_principal.png', dpi=150, bbox_inches='tight')
plt.close()
")

# Ejecutar código Python
py_run_string(codigo_python_principal)
```

```{r generar_opciones_respuesta, echo=FALSE, results="hide"}
# Generar las 4 opciones de respuesta con diferentes representaciones

# OPCIÓN A: Gráfica de barras horizontales (similar a la principal pero con datos específicos)
codigo_python_opcion_a <- paste0("
import matplotlib.pyplot as plt
import numpy as np

# Datos para opción A
paises_a = ['", paste(datos_exportacion$pais, collapse = "', '"), "']
valores_a = [", paste(datos_exportacion$año_2010, collapse = ", "), "]
colores_a = [", paste(sprintf("'#%02X%02X%02X'",
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[1])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[2])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[3]))
), collapse = ", "), "]

fig, ax = plt.subplots(figsize=(8, 5))
y_pos = np.arange(len(paises_a))
bars = ax.barh(y_pos, valores_a, color=colores_a, alpha=0.8)

ax.set_yticks(y_pos)
ax.set_yticklabels(paises_a, fontsize=9)
ax.set_xlabel('Exportaciones 2010', fontsize=10)
ax.set_title('Opción A', fontsize=12, fontweight='bold')

for i, bar in enumerate(bars):
    width = bar.get_width()
    ax.text(width + 5, bar.get_y() + bar.get_height()/2,
            f'{int(width)}', ha='left', va='center', fontsize=8)

plt.tight_layout()
plt.savefig('opcion_a.png', dpi=150, bbox_inches='tight')
plt.close()
")

# OPCIÓN B: Gráfica circular
codigo_python_opcion_b <- paste0("
import matplotlib.pyplot as plt

# Datos para opción B (solo 2010)
valores_b = [", paste(datos_exportacion$año_2010, collapse = ", "), "]
labels_b = ['", paste(datos_exportacion$pais, collapse = "', '"), "']
colores_b = [", paste(sprintf("'#%02X%02X%02X'",
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[1])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[2])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[3]))
), collapse = ", "), "]

fig, ax = plt.subplots(figsize=(8, 6))
wedges, texts, autotexts = ax.pie(valores_b, labels=labels_b, colors=colores_b,
                                  autopct='%1.0f', startangle=90)

ax.set_title('Opción B - Distribución 2010', fontsize=12, fontweight='bold')
plt.tight_layout()
plt.savefig('opcion_b.png', dpi=150, bbox_inches='tight')
plt.close()
")

# OPCIÓN C: Gráfica de líneas
codigo_python_opcion_c <- paste0("
import matplotlib.pyplot as plt
import numpy as np

# Datos para opción C
años_c = [2008, 2009, 2010]
paises_c = ['", paste(datos_exportacion$pais, collapse = "', '"), "']

fig, ax = plt.subplots(figsize=(8, 6))

# Crear líneas para cada país
for i, pais in enumerate(paises_c):
    valores_pais = [", paste(datos_exportacion$año_2008, collapse = ", "), "][i], [", paste(datos_exportacion$año_2009, collapse = ", "), "][i], [", paste(datos_exportacion$año_2010, collapse = ", "), "][i]
    ax.plot(años_c, valores_pais, marker='o', linewidth=2, label=pais)

ax.set_xlabel('Año', fontsize=10)
ax.set_ylabel('Exportaciones', fontsize=10)
ax.set_title('Opción C - Evolución temporal', fontsize=12, fontweight='bold')
ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('opcion_c.png', dpi=150, bbox_inches='tight')
plt.close()
")

# OPCIÓN D: Gráfica de barras verticales
codigo_python_opcion_d <- paste0("
import matplotlib.pyplot as plt
import numpy as np

# Datos para opción D
paises_d = ['", paste(datos_exportacion$pais, collapse = "', '"), "']
valores_d = [", paste(datos_exportacion$año_2009, collapse = ", "), "]
colores_d = [", paste(sprintf("'#%02X%02X%02X'",
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[1])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[2])),
  as.integer(sapply(strsplit(colores_paises, ","), function(x) x[3]))
), collapse = ", "), "]

fig, ax = plt.subplots(figsize=(8, 6))
x_pos = np.arange(len(paises_d))
bars = ax.bar(x_pos, valores_d, color=colores_d, alpha=0.8)

ax.set_xticks(x_pos)
ax.set_xticklabels(paises_d, rotation=45, ha='right', fontsize=9)
ax.set_ylabel('Exportaciones 2009', fontsize=10)
ax.set_title('Opción D', fontsize=12, fontweight='bold')

for i, bar in enumerate(bars):
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2, height + 5,
            f'{int(height)}', ha='center', va='bottom', fontsize=8)

plt.tight_layout()
plt.savefig('opcion_d.png', dpi=150, bbox_inches='tight')
plt.close()
")

# Ejecutar códigos Python para generar todas las opciones
py_run_string(codigo_python_opcion_a)
py_run_string(codigo_python_opcion_b)
py_run_string(codigo_python_opcion_c)
py_run_string(codigo_python_opcion_d)
```

```{r sistema_distractores_avanzado, echo=FALSE, results="hide"}
# Sistema avanzado de distractores para ICFES
# Generar múltiples tipos de distractores y seleccionar estratégicamente

# Respuesta correcta: La opción que mejor representa los datos de la gráfica principal
respuesta_correcta <- "La opción C muestra correctamente toda la información de la gráfica original, incluyendo los tres años (2008, 2009, 2010) y todos los países de destino."

# Generar 8+ tipos diferentes de distractores
distractores_disponibles <- c(
  "La opción A representa mejor los datos porque muestra claramente los valores de 2010 con mayor detalle.",
  "La opción B es la más adecuada ya que permite visualizar la distribución porcentual entre países de destino.",
  "La opción D es correcta porque presenta los datos de 2009 de manera más clara y organizada.",
  "La opción A no es apropiada porque solo muestra un año específico y no toda la información temporal.",
  "La opción B es incorrecta porque las gráficas circulares no muestran la evolución temporal de los datos.",
  "La opción D presenta solo información parcial que no incluye todos los años mostrados en la gráfica original.",
  "La opción A tiene limitaciones porque no permite comparar la evolución entre diferentes años.",
  "Ninguna de las opciones representa completamente la información temporal mostrada en la gráfica original."
)

# Decidir si incluir valores duplicados con justificaciones diferentes (30% probabilidad)
permitir_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

if(permitir_duplicados) {
  # Incluir justificación incorrecta para la respuesta correcta
  justificacion_incorrecta <- "La opción C es correcta porque utiliza colores más llamativos que facilitan la interpretación visual."

  # Seleccionar 1 duplicado + 2 diferentes
  distractores_seleccionados <- c(
    justificacion_incorrecta,
    sample(distractores_disponibles, 2)
  )
} else {
  # Modo tradicional: todos los valores diferentes
  distractores_seleccionados <- sample(distractores_disponibles, 3)
}

# Crear opciones finales
opciones_finales <- c(respuesta_correcta, distractores_seleccionados)
opciones_finales <- sample(opciones_finales)  # Mezclar orden

# Determinar patrón de solución
solucion <- rep("0", 4)
posicion_correcta <- which(opciones_finales == respuesta_correcta)
solucion[posicion_correcta] <- "1"
solucion_final <- paste(solucion, collapse="")
```

Question
========

La gráfica muestra las exportaciones del sector `r contexto_sector` de un país hacia diferentes destinos durante el período 2008-2010, expresadas en `r unidad_medida`. Los principales productos exportados incluyen `r productos_ejemplo`, entre otros.

```{r mostrar_grafico_principal, echo=FALSE, results='asis', fig.align="center"}
# Mostrar la imagen del gráfico principal
cat("![](grafico_exportaciones_principal.png){width=85%}")
```

Otra representación que muestra toda la información de la gráfica anterior es:

Answerlist
----------

```{r options, echo=FALSE, results='asis'}
# Mostrar las opciones de gráficas usando método validado de ejemplos funcionales
cat("-\n")
cat("![](opcion_a.png){width=70%}\n\n")
cat("-\n")
cat("![](opcion_b.png){width=70%}\n\n")
cat("-\n")
cat("![](opcion_c.png){width=70%}\n\n")
cat("-\n")
cat("![](opcion_d.png){width=70%}\n\n")
```

Solution
========

Para resolver este problema, debemos analizar qué representación gráfica muestra **toda la información** de la gráfica original de manera equivalente.

### Análisis de la gráfica original:
La gráfica principal muestra:
- **Datos de tres años**: 2008, 2009 y 2010
- **Cinco destinos**: `r paste(paises_destino, collapse = ", ")`
- **Valores específicos** para cada combinación año-destino
- **Comparación temporal** entre los diferentes años

### Análisis de cada opción:

**Opción A**: Muestra únicamente los datos de 2010 en formato de barras horizontales. Aunque mantiene la información de todos los destinos para ese año específico, **no incluye** los datos de 2008 y 2009.

**Opción B**: Presenta una gráfica circular con los datos de 2010, mostrando la distribución porcentual entre destinos. **No incluye** información temporal de los otros años.

**Opción C**: Muestra la evolución temporal de las exportaciones mediante líneas, incluyendo **todos los años** (2008, 2009, 2010) y **todos los destinos**. Esta representación permite visualizar tanto los valores específicos como las tendencias temporales.

**Opción D**: Presenta únicamente los datos de 2009 en formato de barras verticales. **No incluye** los datos de 2008 y 2010.

### Conclusión:
La **Opción C** es la única que muestra **toda la información** de la gráfica original, incluyendo:
- Los tres años de datos (2008, 2009, 2010)
- Los cinco destinos de exportación
- Los valores específicos para cada combinación
- La posibilidad de comparar tanto entre destinos como entre años

Answerlist
----------
- `r if(posicion_correcta == 1) "Verdadero" else "Falso"`. `r if(posicion_correcta == 1) "Esta opción identifica correctamente la representación que muestra toda la información." else "Esta opción no considera todos los aspectos de la información presentada."`
- `r if(posicion_correcta == 2) "Verdadero" else "Falso"`. `r if(posicion_correcta == 2) "Esta opción identifica correctamente la representación que muestra toda la información." else "Esta opción no considera todos los aspectos de la información presentada."`
- `r if(posicion_correcta == 3) "Verdadero" else "Falso"`. `r if(posicion_correcta == 3) "Esta opción identifica correctamente la representación que muestra toda la información." else "Esta opción no considera todos los aspectos de la información presentada."`
- `r if(posicion_correcta == 4) "Verdadero" else "Falso"`. `r if(posicion_correcta == 4) "Esta opción identifica correctamente la representación que muestra toda la información." else "Esta opción no considera todos los aspectos de la información presentada."`

Meta-information
================
exname: Exportaciones industriales interpretación representación
extype: schoice
exsolution: `r solucion_final`
exshuffle: TRUE
exsection: Estadística/Interpretación de datos
