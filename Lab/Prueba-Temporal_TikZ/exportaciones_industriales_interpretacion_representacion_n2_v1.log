This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=pdflatex 2025.6.14)  29 JUN 2025 17:52
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**exportaciones_industriales_interpretacion_representacion_n2_v1.tex
(./exportaciones_industriales_interpretacion_representacion_n2_v1.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-05-26>
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/05/18 v2.17x AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen149
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count283
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count284
\leftroot@=\count285
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count286
\DOTSCASE@=\count287
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count288
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count289
\dotsspace@=\muskip17
\c@parentequation=\count290
\dspbrk@lvl=\count291
\tag@help=\toks18
\row@=\count292
\column@=\count293
\maxfields@=\count294
\andhelp@=\toks19
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks22
\inpenc@posthook=\toks23
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count295
)
\MT@toks=\toks25
\MT@tempbox=\box55
\MT@count=\count296
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks26
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen158
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count297
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2025/02/11 v3.2a Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip54
)
Package microtype Info: Loading configuration file microtype.cfg.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count298
\Gm@cntv=\count299
\c@Gm@tempcnt=\count300
\Gm@bindingoffset=\dimen159
\Gm@wd@mp=\dimen160
\Gm@odd@mp=\dimen161
\Gm@even@mp=\dimen162
\Gm@layoutwidth=\dimen163
\Gm@layoutheight=\dimen164
\Gm@layouthoffset=\dimen165
\Gm@layoutvoffset=\dimen166
\Gm@dimlist=\toks27
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen167
\Gin@req@width=\dimen168
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count301
\float@exts=\toks28
\float@box=\box56
\@float@everytoks=\toks29
\@floatcapt=\box57
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks30
\pgfutil@tempdima=\dimen169
\pgfutil@tempdimb=\dimen170
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box58
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks31
\pgfkeys@temptoks=\toks32
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks33
))
\pgf@x=\dimen171
\pgf@y=\dimen172
\pgf@xa=\dimen173
\pgf@ya=\dimen174
\pgf@xb=\dimen175
\pgf@yb=\dimen176
\pgf@xc=\dimen177
\pgf@yc=\dimen178
\pgf@xd=\dimen179
\pgf@yd=\dimen180
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count302
\c@pgf@countb=\count303
\c@pgf@countc=\count304
\c@pgf@countd=\count305
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36
\pgf@sys@id@count=\count306
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count307
\pgfsyssoftpath@bigbuffer@items=\count308
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen181
\pgfmath@count=\count309
\pgfmath@box=\box59
\pgfmath@toks=\toks37
\pgfmath@stack@operand=\toks38
\pgfmath@stack@operation=\toks39
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count310
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen182
\pgf@picmaxx=\dimen183
\pgf@picminy=\dimen184
\pgf@picmaxy=\dimen185
\pgf@pathminx=\dimen186
\pgf@pathmaxx=\dimen187
\pgf@pathminy=\dimen188
\pgf@pathmaxy=\dimen189
\pgf@xx=\dimen190
\pgf@xy=\dimen191
\pgf@yx=\dimen192
\pgf@yy=\dimen193
\pgf@zx=\dimen194
\pgf@zy=\dimen195
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen196
\pgf@path@lasty=\dimen197
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen198
\pgf@shorten@start@additional=\dimen199
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count311
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen256
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen257
\pgf@pt@y=\dimen258
\pgf@pt@temp=\dimen259
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen260
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen261
\pgf@sys@shading@range@num=\count312
\pgf@shadingcount=\count313
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen262
\pgf@nodesepend=\dimen263
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen264
\pgffor@skip=\dimen265
\pgffor@stack=\toks40
\pgffor@toks=\toks41
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count314
\pgfplotmarksize=\dimen266
)
\tikz@lastx=\dimen267
\tikz@lasty=\dimen268
\tikz@lastxsaved=\dimen269
\tikz@lastysaved=\dimen270
\tikz@lastmovetox=\dimen271
\tikz@lastmovetoy=\dimen272
\tikzleveldistance=\dimen273
\tikzsiblingdistance=\dimen274
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count315
\tikznumberofchildren=\count316
\tikznumberofcurrentchild=\count317
\tikz@fig@count=\count318
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count319
\pgfmatrixcurrentcolumn=\count320
\pgf@matrix@numberofcolumns=\count321
)
\tikz@expandcount=\count322
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-05-20 v7.01m Hypertext links for LaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count323
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen275
\Hy@linkcounter=\count324
\Hy@pagecounter=\count325
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-05-20 v7.01m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count326
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2025-05-20 v7.01m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count327
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen276
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count328
\Field@Width=\dimen277
\Fld@charsize=\dimen278
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count329
\c@Item=\count330
\c@Hfootnote=\count331
)
Package hyperref Info: Driver (autodetected): hpdftex.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2025-05-20 v7.01m Hyperref driver for pdfTeX
\Fld@listcount=\count332
\c@bookmark@seq@number=\count333
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip55
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bkm-pdftex.def
File: bkm-pdftex.def 2023-12-10 v1.31 bookmark driver for pdfTeX and luaTeX (HO)
\BKM@id=\count334
))
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 74.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2025-04-14 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count335
) (./exportaciones_industriales_interpretacion_representacion_n2_v1.aux)
\openout1 = `exportaciones_industriales_interpretacion_representacion_n2_v1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 74.
LaTeX Font Info:    ... okay on input line 74.
LaTeX Info: Redefining \microtypecontext on input line 74.
Package microtype Info: Applying patch `item' on input line 74.
Package microtype Info: Applying patch `toc' on input line 74.
Package microtype Info: Applying patch `eqnum' on input line 74.
Package microtype Info: Applying patch `footnote' on input line 74.
Package microtype Info: Applying patch `verbatim' on input line 74.
LaTeX Info: Redefining \microtypesetup on input line 74.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using protrusion set `basicmath'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.

LaTeX Warning: Command \showhyphens   has changed.
               Check if current package is valid.

LaTeX Info: Redefining \showhyphens on input line 74.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman (RS)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 74.
<grafico_exportaciones_principal.png, id=7, 859.5312pt x 570.933pt>
File: grafico_exportaciones_principal.png Graphic file (type png)
<use grafico_exportaciones_principal.png>
Package pdftex.def Info: grafico_exportaciones_principal.png  used on input line 83.
(pdftex.def)             Requested size: 399.29753pt x 265.22845pt.
<opcion_a.png, id=9, 570.4512pt x 353.6412pt>
File: opcion_a.png Graphic file (type png)
<use opcion_a.png>
Package pdftex.def Info: opcion_a.png  used on input line 89.
(pdftex.def)             Requested size: 328.8338pt x 203.85474pt.
<opcion_b.png, id=10, 446.6286pt x 426.8748pt>
File: opcion_b.png Graphic file (type png)
<use opcion_b.png>
Package pdftex.def Info: opcion_b.png  used on input line 92.
(pdftex.def)             Requested size: 328.82349pt x 314.28006pt.


[1

{/home/<USER>/.TinyTeX/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/home/<USER>/.TinyTeX/texmf-dist/fonts/enc/dvips/lm/lm-ec.enc} <./grafico_exportaciones_principal.png> <./opcion_a.png>]
<opcion_c.png, id=22, 566.115pt x 425.9112pt>
File: opcion_c.png Graphic file (type png)
<use opcion_c.png>
Package pdftex.def Info: opcion_c.png  used on input line 95.
(pdftex.def)             Requested size: 328.83067pt x 247.3926pt.
<opcion_d.png, id=23, 569.9694pt x 426.393pt>
File: opcion_d.png Graphic file (type png)
<use opcion_d.png>
Package pdftex.def Info: opcion_d.png  used on input line 98.
(pdftex.def)             Requested size: 328.84308pt x 246.00687pt.


[2 <./opcion_b.png> <./opcion_c.png>]
LaTeX Font Info:    Trying to load font information for TS1+lmr on input line 105.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/ts1lmr.fd
File: ts1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)

[3{/home/<USER>/.TinyTeX/texmf-dist/fonts/enc/dvips/lm/lm-ts1.enc} <./opcion_d.png>]

[4] (./exportaciones_industriales_interpretacion_representacion_n2_v1.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-05-26>
 ***********
 ) 
Here is how much of TeX's memory you used:
 22892 strings out of 469849
 423401 string characters out of 5482617
 830293 words of memory out of 5000000
 50792 multiletter control sequences out of 15000+600000
 648302 words of font info for 75 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 84i,6n,93p,1059b,455s stack positions out of 10000i,1000n,20000p,200000b,200000s
</home/<USER>/.TinyTeX/texmf-dist/fonts/type1/public/lm/lmbx10.pfb></home/<USER>/.TinyTeX/texmf-dist/fonts/type1/public/lm/lmbx12.pfb></home/<USER>/.TinyTeX/texmf-dist/fonts/type1/public/lm/lmr10.pfb>
Output written on exportaciones_industriales_interpretacion_representacion_n2_v1.pdf (4 pages, 324309 bytes).
PDF statistics:
 95 PDF objects out of 1000 (max. 8388607)
 71 compressed objects within 1 object stream
 13 named destinations out of 1000 (max. 500000)
 14438 words of extra memory for PDF output out of 17280 (max. 10000000)

